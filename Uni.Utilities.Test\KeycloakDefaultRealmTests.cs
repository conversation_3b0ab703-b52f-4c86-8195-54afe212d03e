using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using UNI.Utilities.Keycloak.Services.Roles;
using Xunit;
using Xunit.Abstractions;

namespace Uni.Utilities.Test
{
    public class KeycloakDefaultRealmTests
    {
        private readonly ILogger<KeycloakDefaultRealmTests> _logger;

        public KeycloakDefaultRealmTests(ITestOutputHelper output)
        {
            var loggerFactory = LoggerFactory.Create(builder =>
                builder.AddProvider(new TestLoggerProvider(output)));
            _logger = loggerFactory.CreateLogger<KeycloakDefaultRealmTests>();
        }

        [Fact]
        public void RoleService_Should_Support_Default_Realm()
        {
            // Arrange
            var httpClient = new HttpClient();
            var defaultRealm = "test-default-realm";
            
            // Act
            var roleService = new RoleService(httpClient, defaultRealm);
            
            // Assert
            Assert.NotNull(roleService);
            _logger.LogInformation("✅ RoleService correctly accepts default realm parameter");
        }

        [Fact]
        public async Task RoleService_Should_Throw_When_Default_Realm_Not_Configured()
        {
            // Arrange
            var httpClient = new HttpClient();
            var roleService = new RoleService(httpClient); // No default realm
            
            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => roleService.GetRealmRolesAsync(cancellationToken: CancellationToken.None));
            
            Assert.Contains("Default realm is not configured", exception.Message);
            _logger.LogInformation("✅ RoleService correctly throws when default realm is not configured");
        }

        [Fact]
        public async Task RoleService_Should_Use_Default_Realm_When_Available()
        {
            // Arrange
            var httpClient = new HttpClient();
            var defaultRealm = "test-default-realm";
            var roleService = new RoleService(httpClient, defaultRealm);
            
            // Act & Assert - Should not throw InvalidOperationException
            try
            {
                await roleService.GetRealmRolesAsync(cancellationToken: CancellationToken.None);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("Default realm is not configured"))
            {
                Assert.Fail("Should not throw InvalidOperationException when default realm is configured");
            }
            catch (Exception)
            {
                // Other exceptions are expected (network errors, etc.)
                _logger.LogInformation("✅ RoleService correctly uses default realm (network error expected)");
            }
        }

        [Fact]
        public async Task RoleService_GetRealmRoleAsync_Should_Use_Default_Realm()
        {
            // Arrange
            var httpClient = new HttpClient();
            var defaultRealm = "test-default-realm";
            var roleService = new RoleService(httpClient, defaultRealm);
            
            // Act & Assert - Should not throw InvalidOperationException
            try
            {
                await roleService.GetRealmRoleAsync("test-role", CancellationToken.None);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("Default realm is not configured"))
            {
                Assert.Fail("Should not throw InvalidOperationException when default realm is configured");
            }
            catch (Exception)
            {
                // Other exceptions are expected (network errors, etc.)
                _logger.LogInformation("✅ RoleService.GetRealmRoleAsync correctly uses default realm (network error expected)");
            }
        }
    }

    // Helper classes for test logging
    public class TestLoggerProvider : ILoggerProvider
    {
        private readonly ITestOutputHelper _output;

        public TestLoggerProvider(ITestOutputHelper output)
        {
            _output = output;
        }

        public ILogger CreateLogger(string categoryName)
        {
            return new TestLogger(_output, categoryName);
        }

        public void Dispose() { }
    }

    public class TestLogger : ILogger
    {
        private readonly ITestOutputHelper _output;
        private readonly string _categoryName;

        public TestLogger(ITestOutputHelper output, string categoryName)
        {
            _output = output;
            _categoryName = categoryName;
        }

        public IDisposable BeginScope<TState>(TState state) => null!;

        public bool IsEnabled(LogLevel logLevel) => true;

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            _output.WriteLine($"[{logLevel}] {_categoryName}: {formatter(state, exception)}");
        }
    }
}

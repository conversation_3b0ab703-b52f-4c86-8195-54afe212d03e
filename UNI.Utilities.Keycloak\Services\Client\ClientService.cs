using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using UNI.Utilities.HttpClientExtension;
using UNI.Utilities.Keycloak.Models;

namespace UNI.Utilities.Keycloak.Services.Client;

public class ClientService : ServiceBase, IClientService
{
    /// <summary>
    /// ctor
    /// </summary>
    /// <param name="client"></param>
    public ClientService(HttpClient client) : base(client)
    {
    }

    /// <summary>
    /// ctor with default realm
    /// </summary>
    /// <param name="client"></param>
    /// <param name="defaultRealm"></param>
    public ClientService(HttpClient client, string defaultRealm) : base(client, defaultRealm)
    {
    }

    public async Task<bool?> CreateClientAsync(string realm, ClientRepresentationDto client, CancellationToken cancellationToken = default)
    {
        var rs = await HttpClient.PostJsonAsync<string>(Endpoints.ClientEndpoints.Clients(realm), client,
            cancellationToken);
        return rs.IsSuccessStatusCode;
    }

    public async Task<ClientRepresentation?> GetClientAsync(string realm, string clientId, CancellationToken cancellationToken = default)
    {
        var rs = await GetClientsAsync(realm, clientId, cancellationToken: cancellationToken);
        return rs?.FirstOrDefault();
    }

    public async Task<IEnumerable<ClientRepresentation>?> GetClientsAsync(string realm, string? clientId = null, bool? viewableOnly = null, int? first = null, int? max = null,
        string? search = null, CancellationToken cancellationToken = default)
    {
        var rs = await HttpClient.GetAsync<IEnumerable<ClientRepresentation>>(Endpoints.ClientEndpoints.Clients(realm, clientId, viewableOnly, first, max, search), cancellationToken);
        return rs.Content;
    }

    public async Task DeleteClientAsync(string realm, string clientId, CancellationToken cancellationToken = default)
    {
        var rs = await HttpClient.DeleteAsync(Endpoints.ClientEndpoints.Client(realm, clientId), cancellationToken);
    }
}
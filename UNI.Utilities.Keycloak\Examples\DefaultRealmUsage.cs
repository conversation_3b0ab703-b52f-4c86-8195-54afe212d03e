using System;
using System.Net.Http;
using System.Threading.Tasks;
using UNI.Utilities.Keycloak.Models;
using UNI.Utilities.Keycloak.Services.Roles;

namespace UNI.Utilities.Keycloak.Examples
{
    /// <summary>
    /// Example demonstrating how to use the default realm functionality
    /// </summary>
    public class DefaultRealmUsage
    {
        public async Task ExampleUsage()
        {
            // Configure settings with default realm
            var settings = new Settings
            {
                BaseUrl = "https://your-keycloak-server.com",
                Realm = "my-default-realm", // This will be used as default realm
                Credentials = new Credentials
                {
                    ClientId = "your-client-id",
                    ClientSecret = "your-client-secret"
                }
            };

            var httpClient = new HttpClient();
            
            // Create KeycloakClient - it will pass the default realm to all services
            var keycloakClient = new KeycloakClient(httpClient, settings);

            // Now you can use methods without specifying realm - they will use the default realm
            try
            {
                // These methods will use the default realm from settings
                var realmRoles = await keycloakClient.Roles.GetRealmRolesAsync();
                var specificRole = await keycloakClient.Roles.GetRealmRoleAsync("admin");

                // You can still specify a different realm if needed
                var otherRealmRoles = await keycloakClient.Roles.GetRealmRolesAsync("other-realm");
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("Default realm is not configured"))
            {
                // This would happen if you create a service without default realm
                // and try to use methods without realm parameter
                Console.WriteLine("Default realm not configured!");
            }

            // Alternative: Create RoleService directly with default realm
            var roleService = new RoleService(httpClient, "my-default-realm");
            
            // These will use the default realm
            var roles = await roleService.GetRealmRolesAsync();
            var role = await roleService.GetRealmRoleAsync("admin");
        }
    }
}

﻿using System;
using System.Collections.Generic;

namespace UNI.Utilities.Keycloak.Models
{
    public class Endpoints
    {
        private const string AdminUriScheme = "/admin/realms";
        public string Realm { get; } = "master";
        public string Users => $"{AdminUriScheme}/{Realm}/users";
        public string Token => $"/realms/{Realm}/protocol/openid-connect/token";
        [Obsolete("Use RealmEndpoints instead")]
        public string User(string userId) => $"{Users}/{userId}";
        [Obsolete("Use RealmEndpoints instead")]
        public string ResetPassword(string userId) => $"{AdminUriScheme}/{Realm}/users/{userId}/reset-password";
        [Obsolete("Use RealmEndpoints instead")]
        public string RemoveSession(string sectionId) => $"/realms/{Realm}/account/{sectionId}";
        [Obsolete("Use RealmEndpoints instead")]
        public string RemoveAllUserSessions(string userId) => $"/admin/realms/{Realm}/users/{userId}/logout";

        public Endpoints()
        {

        }

        public Endpoints(string realm)
        {
            Realm = realm;
        }
        public static class RealmEndpoints
        {
            public static string Realms() => "/admin/realms";
            public static string Realms(string realmName) => $"/admin/realms/{realmName}";
        }
        public static class ClientEndpoints
        {
            public static string Clients(string realm) => $"/admin/realms/{realm}/clients";

            public static string Clients(string realm, string? clientId, bool? viewableOnly, int? first, int? max, string? search)
            {
                var query = new List<string>();
                if (!string.IsNullOrEmpty(clientId)) query.Add($"clientId={clientId}");
                if (viewableOnly.HasValue) query.Add($"viewableOnly={viewableOnly}");
                if (first.HasValue) query.Add($"first={first}");
                if (max.HasValue) query.Add($"max={max}");
                if (!string.IsNullOrEmpty(search)) query.Add($"search={search}");

                var url = $"/admin/realms/{realm}/clients";
                if (query.Count > 0) url += $"?{string.Join("&", query)}";
                return url;
            }

            public static string Client(string realm, string clientId)
            {
                return $"/admin/realms/{realm}/clients/{clientId}";
            }
        }
        public static class UserEndpoints
        {
            public static string Users(string realm) => $"{AdminUriScheme}/{realm}/users";
            public static string User(string realm, string userId) => $"{Users(realm)}/{userId}";
            public static string ResetPassword(string realm, string userId) => $"{AdminUriScheme}/{realm}/users/{userId}/reset-password";
            public static string RemoveSession(string realm, string sectionId) => $"/realms/{realm}/account/{sectionId}";
            public static string RemoveAllUserSessions(string realm, string userId) => $"/admin/realms/{realm}/users/{userId}/logout";
        }

        public static class RoleEndpoints
        {
            // Realm roles
            public static string RealmRoles(string realm) => $"{AdminUriScheme}/{realm}/roles";
            public static string RealmRole(string realm, string roleName) => $"{RealmRoles(realm)}/{roleName}";
            public static string RealmRoleComposites(string realm, string roleName) => $"{RealmRole(realm, roleName)}/composites";
            public static string RealmRoleUsers(string realm, string roleName) => $"{RealmRole(realm, roleName)}/users";

            // Client roles
            public static string ClientRoles(string realm, string clientId) => $"{AdminUriScheme}/{realm}/clients/{clientId}/roles";
            public static string ClientRole(string realm, string clientId, string roleName) => $"{ClientRoles(realm, clientId)}/{roleName}";
            public static string ClientRoleComposites(string realm, string clientId, string roleName) => $"{ClientRole(realm, clientId, roleName)}/composites";
            public static string ClientRoleUsers(string realm, string clientId, string roleName) => $"{ClientRole(realm, clientId, roleName)}/users";

            // Role mappings
            public static string UserRoleMappings(string realm, string userId) => $"{AdminUriScheme}/{realm}/users/{userId}/role-mappings";
            public static string UserRealmRoleMappings(string realm, string userId) => $"{UserRoleMappings(realm, userId)}/realm";
            public static string UserClientRoleMappings(string realm, string userId, string clientId) => $"{UserRoleMappings(realm, userId)}/clients/{clientId}";

            public static string GroupRoleMappings(string realm, string groupId) => $"{AdminUriScheme}/{realm}/groups/{groupId}/role-mappings";
            public static string GroupRealmRoleMappings(string realm, string groupId) => $"{GroupRoleMappings(realm, groupId)}/realm";
            public static string GroupClientRoleMappings(string realm, string groupId, string clientId) => $"{GroupRoleMappings(realm, groupId)}/clients/{clientId}";
        }
    }
}

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace UNI.Utilities.Keycloak.Models.Users
{
    [Obsolete($"Use {nameof(UserRepresentation)} instead.")]
    public class User
    {

        [JsonPropertyName("id")]
        public string? Id { get; set; }
        [JsonPropertyName("username")]
        public string? Username { get; set; }
        [JsonPropertyName("firstName")]
        public string? FirstName { get; set; }
        [JsonPropertyName("lastName")]
        public string? LastName { get; set; }
        [JsonPropertyName("email")]
        public string? Email { get; set; }
        [JsonPropertyName("emailVerified")]
        public bool? EmailVerified { get; set; }
        [JsonPropertyName("attributes")]
        public IDictionary<string, IEnumerable<string>>? Attributes { get; set; }
        [JsonPropertyName("userProfileMetadata")]
        public UserProfileMetadata? UserProfileMetadata { get; set; }
        [JsonPropertyName("self")]
        public string? Self { get; set; }
        [JsonPropertyName("origin")]
        public string? Origin { get; set; }
        [JsonPropertyName("createdTimestamp")]
        public long CreatedTimestamp { get; set; }
        [JsonPropertyName("enabled")]
        public bool? Enabled { get; set; }
        [JsonPropertyName("totp")]
        public bool? Totp { get; set; }
        [JsonPropertyName("federationLink")]
        public string? FederationLink { get; set; }
        [JsonPropertyName("serviceAccountClientId")]
        public string? ServiceAccountClientId { get; set; }
        [JsonPropertyName("credentials")]
        public List<CredentialRepresentation>? Credentials { get; set; }
        [JsonPropertyName("disableableCredentialTypes")]
        public HashSet<string>? DisableableCredentialTypes { get; set; }
        [JsonPropertyName("requiredActions")]
        public List<string>? RequiredActions { get; set; }
        [JsonPropertyName("federatedIdentities")]
        public List<FederatedIdentityRepresentation>? FederatedIdentities { get; set; }
        [JsonPropertyName("realmRoles")]
        public List<string>? RealmRoles { get; set; }
        [JsonPropertyName("clientRoles")]
        public IDictionary<string, object>? ClientRoles { get; set; }
        [JsonPropertyName("clientConsents")]
        public UserConsentRepresentation? ClientConsents { get; set; }
        [JsonPropertyName("notBefore")]
        public int NotBefore { get; set; }
        [JsonPropertyName("applicationRoles")]
        public IDictionary<string, object>? ApplicationRoles { get; set; }
        [JsonPropertyName("socialLinks")]
        public SocialLinkRepresentation? SocialLinks { get; set; }
        [JsonPropertyName("groups")]
        public List<string>? Groups { get; set; }
        [JsonPropertyName("access")]
        public IDictionary<string,bool>? Access { get; set; }
    }
}
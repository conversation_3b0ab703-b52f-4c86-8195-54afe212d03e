using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using UNI.Utilities.Keycloak.Models;
using UNI.Utilities.Keycloak.Models.Users;

namespace UNI.Utilities.Keycloak.Services.Roles
{
    public interface IRoleService
    {
        #region Realm Roles
        
        /// <summary>
        /// Get all realm roles
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="briefRepresentation">Brief representation</param>
        /// <param name="first">First result</param>
        /// <param name="max">Maximum results</param>
        /// <param name="search">Search string</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of realm roles</returns>
        Task<IEnumerable<RoleRepresentation>?> GetRealmRolesAsync(string realm, bool? briefRepresentation = null,
            int? first = null, int? max = null, string? search = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get all realm roles using default realm
        /// </summary>
        /// <param name="briefRepresentation">Brief representation</param>
        /// <param name="first">First result</param>
        /// <param name="max">Maximum results</param>
        /// <param name="search">Search string</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of realm roles</returns>
        Task<IEnumerable<RoleRepresentation>?> GetRealmRolesAsync(bool? briefRepresentation = null,
            int? first = null, int? max = null, string? search = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a specific realm role by name
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="roleName">Role name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Role representation</returns>
        Task<RoleRepresentation?> GetRealmRoleAsync(string realm, string roleName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a specific realm role by name using default realm
        /// </summary>
        /// <param name="roleName">Role name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Role representation</returns>
        Task<RoleRepresentation?> GetRealmRoleAsync(string roleName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a new realm role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="role">Role representation</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> CreateRealmRoleAsync(string realm, RoleRepresentation role, CancellationToken cancellationToken = default);

        /// <summary>
        /// Update a realm role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="roleName">Role name</param>
        /// <param name="role">Updated role representation</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> UpdateRealmRoleAsync(string realm, string roleName, RoleRepresentation role, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a realm role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="roleName">Role name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> DeleteRealmRoleAsync(string realm, string roleName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get composite roles for a realm role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="roleName">Role name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of composite roles</returns>
        Task<IEnumerable<RoleRepresentation>?> GetRealmRoleCompositesAsync(string realm, string roleName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Add composite roles to a realm role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="roleName">Role name</param>
        /// <param name="roles">Roles to add as composites</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> AddRealmRoleCompositesAsync(string realm, string roleName, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default);

        /// <summary>
        /// Remove composite roles from a realm role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="roleName">Role name</param>
        /// <param name="roles">Roles to remove from composites</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> RemoveRealmRoleCompositesAsync(string realm, string roleName, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get users assigned to a realm role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="roleName">Role name</param>
        /// <param name="first">First result</param>
        /// <param name="max">Maximum results</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of users</returns>
        Task<IEnumerable<UserRepresentation>?> GetRealmRoleUsersAsync(string realm, string roleName, int? first = null, int? max = null, CancellationToken cancellationToken = default);

        #endregion

        #region Client Roles

        /// <summary>
        /// Get all client roles
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="briefRepresentation">Brief representation</param>
        /// <param name="first">First result</param>
        /// <param name="max">Maximum results</param>
        /// <param name="search">Search string</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of client roles</returns>
        Task<IEnumerable<RoleRepresentation>?> GetClientRolesAsync(string realm, string clientId, bool? briefRepresentation = null,
            int? first = null, int? max = null, string? search = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a specific client role by name
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="roleName">Role name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Role representation</returns>
        Task<RoleRepresentation?> GetClientRoleAsync(string realm, string clientId, string roleName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a new client role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="role">Role representation</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> CreateClientRoleAsync(string realm, string clientId, RoleRepresentation role, CancellationToken cancellationToken = default);

        /// <summary>
        /// Update a client role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="roleName">Role name</param>
        /// <param name="role">Updated role representation</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> UpdateClientRoleAsync(string realm, string clientId, string roleName, RoleRepresentation role, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a client role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="roleName">Role name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> DeleteClientRoleAsync(string realm, string clientId, string roleName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get composite roles for a client role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="roleName">Role name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of composite roles</returns>
        Task<IEnumerable<RoleRepresentation>?> GetClientRoleCompositesAsync(string realm, string clientId, string roleName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Add composite roles to a client role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="roleName">Role name</param>
        /// <param name="roles">Roles to add as composites</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> AddClientRoleCompositesAsync(string realm, string clientId, string roleName, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default);

        /// <summary>
        /// Remove composite roles from a client role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="roleName">Role name</param>
        /// <param name="roles">Roles to remove from composites</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> RemoveClientRoleCompositesAsync(string realm, string clientId, string roleName, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get users assigned to a client role
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="roleName">Role name</param>
        /// <param name="first">First result</param>
        /// <param name="max">Maximum results</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of users</returns>
        Task<IEnumerable<UserRepresentation>?> GetClientRoleUsersAsync(string realm, string clientId, string roleName, int? first = null, int? max = null, CancellationToken cancellationToken = default);

        #endregion

        #region Role Mappings

        /// <summary>
        /// Get role mappings for a user
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="userId">User ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Role mappings</returns>
        Task<RolesRepresentation?> GetUserRoleMappingsAsync(string realm, string userId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get realm role mappings for a user
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="userId">User ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of realm roles</returns>
        Task<IEnumerable<RoleRepresentation>?> GetUserRealmRoleMappingsAsync(string realm, string userId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Add realm role mappings to a user
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="userId">User ID</param>
        /// <param name="roles">Roles to assign</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> AddUserRealmRoleMappingsAsync(string realm, string userId, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default);

        /// <summary>
        /// Remove realm role mappings from a user
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="userId">User ID</param>
        /// <param name="roles">Roles to remove</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> RemoveUserRealmRoleMappingsAsync(string realm, string userId, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get client role mappings for a user
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="userId">User ID</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of client roles</returns>
        Task<IEnumerable<RoleRepresentation>?> GetUserClientRoleMappingsAsync(string realm, string userId, string clientId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Add client role mappings to a user
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="userId">User ID</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="roles">Roles to assign</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> AddUserClientRoleMappingsAsync(string realm, string userId, string clientId, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default);

        /// <summary>
        /// Remove client role mappings from a user
        /// </summary>
        /// <param name="realm">Realm name</param>
        /// <param name="userId">User ID</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="roles">Roles to remove</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> RemoveUserClientRoleMappingsAsync(string realm, string userId, string clientId, IEnumerable<RoleRepresentation> roles, CancellationToken cancellationToken = default);

        #endregion
    }
}

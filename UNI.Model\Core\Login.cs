﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UNI.Model.Core
{
    public class LoginRequest
    {
        public string Username { get; set; }
        public string Password { get; set; }
        public string PlayerId { get; set; }
    }
    public class LoginResponse
    {
        public string token { get; set; }
        public string refreshToken { get; set; }
    }
    public class TokenRequest
    {
        public string token { get; set; }
    }
    public class UserPassWordChange
    {
        public string userId { get; set; }
        public string OldPassWord { get; set; }
        public string NewPassWord { get; set; }
    }
    public class UserPassWordIn
    {
        public string userName { get; set; }
        public string passWord { get; set; }
    }
    public class UserStatisticData
    {
        public int soLuongTruyCap { get; set; } = 0;
        public int soNguoiBan { get; set; } = 0;
        public int tongSoSanPham { get; set; } = 0;
        public int soSanPhamMoi { get; set; } = 0;
        public int soLuongGiaoDich { get; set; } = 0;
        public int tongSoDonHangThanhCong { get; set; } = 0;
        public int tongSoDonHangKhongThanhCong { get; set; } = 0;
        public decimal tongGiaTriGiaoDich { get; set; } = 0;
    }
}

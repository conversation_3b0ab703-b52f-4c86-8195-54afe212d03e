using UNI.Utilities.Keycloak.Services.Client;
using UNI.Utilities.Keycloak.Services.Realm;
using UNI.Utilities.Keycloak.Services.Roles;
using UNI.Utilities.Keycloak.Services.Users;

namespace UNI.Utilities.Keycloak
{
    public interface IKeycloakClient: IKeycloakUserService
    {
        IRealmService Realms { get; }
        IClientService Clients { get; }
        IUserService Users { get; }
        IRoleService Roles { get; }
    }
}